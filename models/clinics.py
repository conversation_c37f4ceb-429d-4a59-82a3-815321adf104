from typing import Any, Dict, List, Optional

from pydantic import BaseModel


class Motive(BaseModel):
    id: int
    name: str
    open: bool
    instructions: str


class Agenda(BaseModel):
    id: int
    practitioner_id: Optional[int]
    name: str


class ClinicConfig(BaseModel):
    config_id: str
    name: str
    address: str
    openings: Dict[str, Any]
    additional_information: str
    forward_number: Optional[str]

    motives: List[Motive]
    agendas: List[Agenda]
