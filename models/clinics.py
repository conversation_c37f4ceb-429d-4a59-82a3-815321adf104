from typing import Dict, List, Optional

from pydantic import BaseModel


class Motive(BaseModel):
    id: int
    name: str
    open: bool
    instructions: str


class Agenda(BaseModel):
    id: int
    practitioner_id: Optional[int]
    name: str


class ClinicOpening(BaseModel):
    start: str
    end: str
    isOpen: bool


class ClinicConfig(BaseModel):
    config_id: str
    name: str
    address: str
    openings: Dict[str, ClinicOpening]
    additional_information: str
    forward_number: Optional[str]

    motives: List[Motive]
    agendas: List[Agenda]
