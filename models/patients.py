import datetime
from typing import List, Optional

from pydantic import BaseModel

from models.appointments import Appointment


class PatientIdentity(BaseModel):
    id: Optional[str] = None
    first_name: str
    last_name: str
    birthdate: datetime.date

    @property
    def full_name(self) -> str:
        return f"{self.first_name} {self.last_name}"


class DoctolibPatient(PatientIdentity):
    id: str
    phone_number: Optional[str]
    appointments: List[Appointment]
    is_blocked: bool = False

