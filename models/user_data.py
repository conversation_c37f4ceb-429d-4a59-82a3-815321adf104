from enum import Enum, unique
from typing import Dict, Optional

from livekit.agents import Agent, BackgroundAudioPlayer, JobContext, RunContext
from pydantic import BaseModel, Field

from models.appointments import Appointment
from models.availabilities import Availability
from models.calls import CallConfig
from models.clinics import ClinicConfig, Motive
from models.patients import DoctolibPatient, PatientIdentity


class Booking(BaseModel):
    id: str
    patient_id: str


@unique
class CallIntent(Enum):
    BOOKING = "booking"
    RESCHEDULE = "reschedule"
    CANCEL = "cancel"
    DELAY = "delay"
    QUESTION = "question"
    UNDEFINED = "undefined"


class UserData(BaseModel):
    config_id: str
    call_config: CallConfig
    clinic_config: ClinicConfig
    retrieved_patient: Optional[DoctolibPatient] = None

    intent: CallIntent = CallIntent.UNDEFINED
    patient: Optional[PatientIdentity] = None
    motive: Optional[Motive] = None
    appointment_slot: Optional[Availability] = None
    current_appointment: Optional[Appointment] = None
    booking: Optional[Booking] = None
    has_completed_task: bool = False

    audio_player: Optional[BackgroundAudioPlayer] = None
    agents: Dict[str, Agent] = Field(default_factory=dict)
    prev_agent: Optional[Agent] = None

    ctx: JobContext

    class Config:
        arbitrary_types_allowed = True


RunContext_T = RunContext[UserData]
