from datetime import datetime
from typing import Optional

from pydantic import BaseModel


class CallConfig(BaseModel):
    call_id: str
    is_dev: bool = False
    start_time: datetime = datetime.now()

    # Clinic config
    config_id: Optional[str] = None
    clinic_phone_number: Optional[str] = None

    # Patient config
    caller_phone_number: str

    # Bot config
    voice_speed: float = 1.25
