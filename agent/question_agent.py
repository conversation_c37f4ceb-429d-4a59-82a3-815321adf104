from typing import Optional

from agent.base_agent import BaseAgent
from models.clinics import ClinicConfig
from models.patients import DoctolibPatient
from utils.prompts import GENERAL_INSTRUCTIONS


# TODO: probably need to confirm identity before ?
def build_patient_information(retrieved_patient: Optional[DoctolibPatient]) -> str:
    if not retrieved_patient:
        return "Tell the patient we couldn't find any patient file linked to their phone number. So we don't have any information on them."

    return f"""
        Name: {retrieved_patient.full_name}
        Appointments:
        {"\n".join([f"- {a.motive_name} at {a.start_date}" for a in retrieved_patient.appointments])}
    """


def build_system_prompt(
    clinic_config: ClinicConfig, retrieved_patient: Optional[DoctolibPatient]
) -> str:
    return f"""
        {GENERAL_INSTRUCTIONS}
        
        
        # Instructions:
       
        Your responsibilities are to answer any question the patient might have about the clinic and the medical practice. 
        - Take the current conversation context into account to continue the conversation naturally and answer the questions.
        - You are a read-only agent, you can only answer questions and provide information.
            - You can't book, reschedule, cancel or modify an appointment
        - If the patient asks something unrelated, politely redirect them to one of the tasks above.
        - If you don't know the answer to a question about the clinic, don't invent anything, just say you don't know.
        - **DO NOT** answer any medical questions. You are a medical assistant, not a doctor.
        - If you just answered a question, but the patient was doing something else before asking the question (like booking an appointment), mention the previous task and ask if the patient wants to continue with it.
        
        
        ## Clinic information:
        - Name: {clinic_config.name}
        - Address: {clinic_config.address}
        - Openings: {clinic_config.openings}
        - Additional information: {clinic_config.additional_information}
        
        ## Patient information:
        {build_patient_information(retrieved_patient)}
        
        ## Prescription requests:
        If the patient has a requests about their prescription, end the call and tell them you will leave a note for the clinic's office
    """


class QuestionsAgent(BaseAgent):
    def __init__(
        self, clinic_config: ClinicConfig, retrieved_patient: Optional[DoctolibPatient]
    ):
        super().__init__(
            instructions=build_system_prompt(clinic_config, retrieved_patient),
            clinic_config=clinic_config,
        )
