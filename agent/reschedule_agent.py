from datetime import date, datetime
from typing import Annotated, Any, List, Optional
from zoneinfo import ZoneInfo

from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from agent.base_agent import BaseAgent
from models.availabilities import Availability
from models.clinics import ClinicConfig
from models.patients import DoctolibPatient, PatientIdentity
from models.user_data import RunContext_T
from services.appointments import reschedule_appointment
from services.availabilities import find_availabilities
from services.patients import RetrievePatientRequest, retrieve_existing_patient
from utils.dates import format_availability
from utils.prompts import GENERAL_INSTRUCTIONS, build_name_prompt


# TODO: not fan of the "no need to ask", we should definitely build something more robust
def build_system_prompt(retrived_patient: Optional[DoctolibPatient]) -> str:
    return f"""
        {GENERAL_INSTRUCTIONS}
        
        
        # Instructions:
       
        Your goal is to **guide the user through rescheduling an existing medical appointment** from start to finish, following the steps below. 
        - Begin the flow immediately (do not introduce yourself with extra chit-chat) and lead the patient through each step, while adhering to the above guidelines. 
        - The appointment rescheduling is only successful when you have called the `confirm_reschedule` tool with all required details and the user has confirmed everything.
            - If the tool is not called, the rescheduling is not considered successful, try to figure out what is blocking you and try to resolve it
            
        ### 1. Patient identity collection
        {build_name_prompt(retrived_patient)}
        **Important**: No need to ask if the patient already came to the clinic as we are in the rescheduling flow.
        
        ### 2. Appointment selection
        Once the patient is identified, you should see the current pending appointments they have.
        - Always list all the appointments the patient has, and ask them to confirm the one they want to reschedule, even if there's only one.
        - If the patient has no appointment, stop the flow and inform them that there is nothing to reschedule.
        - Once the appointment to reschedule is confirmed, call the `confirm_appointment_to_reschedule` tool with the appointment ID.
        
        ### 3. Find a new date and time
        Ask the patient for the new date and time they want to reschedule the appointment to.
        - Once the patient gave you a date they want to reschedule to, call the `find_availabilities` tool to find availabilities for the patient's appointment motive.
        - If there are availabilities, present them to the patient and ask them to confirm the one they want to reschedule to.
        - Once the patient confirmed the new date and time, call the `confirm_appointment_datetime` tool with the appointment datetime.
        
        ### 4. Reschedule confirmation
        Once the patient has selected the appointment they want to reschedule, and you've found a new appointment slot, confirm the rescheduling with them.
            - If the patient confirms, call the `confirm_reschedule` tool with the appointment ID and the new date and time.
            - If the patient declines, ask them how they want to proceed.
        Once the rescheduling is confirmed, inform the patient that their appointment has been rescheduled and end the flow.
            - Call the `end_call` tool to end the conversation.
    """


class RescheduleAgent(BaseAgent):
    def __init__(
        self, clinic_config: ClinicConfig, retrieved_patient: Optional[DoctolibPatient]
    ):
        super().__init__(
            instructions=build_system_prompt(retrieved_patient),
            clinic_config=clinic_config,
        )
        self.availabilities: List[Availability] = []

    @function_tool()
    async def confirm_identity(
        self,
        context: RunContext_T,
        first_name: Annotated[
            str,
            Field(
                description="The patient's first name, with correct spelling collected from the patient"
            ),
        ],
        last_name: Annotated[
            str,
            Field(
                description="The patient's last name, with correct spelling collected from the patient"
            ),
        ],
        birthdate: Annotated[
            date,
            Field(description="The patient's birthdate, collected from the patient"),
        ],
    ) -> str:
        """
        Called when the user provides their name.
        You will prompt the user with the full spelling of the name and confirm if it matches the patient's identity.
        If the user confirms, call the function again with `has_confirmed` set to `true`.

        Args:
            first_name (str): The patient's first name
            last_name (str): The patient's last name
            birthdate (str): The patient's birthdate in format YYYY-MM-DD
        """
        logger.info(
            f"Confirmed patient identity: {first_name} {last_name} ({birthdate})"
        )

        patient = await retrieve_existing_patient(
            context.userdata.config_id,
            params=RetrievePatientRequest(
                first_name=first_name, last_name=last_name, birthdate=birthdate
            ),
        )

        if not patient:
            return "I could not find your identity in our system. Please try again."

        self.retrieved_patient = patient
        context.userdata.patient = PatientIdentity(
            id=self.retrieved_patient.id,
            first_name=self.retrieved_patient.first_name,
            last_name=self.retrieved_patient.last_name,
            birthdate=self.retrieved_patient.birthdate,
        )

        if not len(patient.appointments):
            return "You have no appointments to reschedule."

        logger.info(patient.appointments)

        return f"""
            You are identified as {first_name} {last_name}. 
            You have the following appointments:
            {"\n".join([f"  - {a.motive_name} on {a.start_date} (appointment_id: {a.id})" for a in patient.appointments])}
            Please confirm the appointment you want to reschedule.
        """

    @function_tool()
    async def confirm_appointment_to_reschedule(
        self,
        context: RunContext_T,
        appointment_id: Annotated[
            str,
            Field(description="The patient's appointment ID they want to reschedule"),
        ],
    ) -> str:
        """
        Called after you confirmed the appointment the patient wants to reschedule.

        Args:
            appointment_id (str): The patient's appointment ID they want to reschedule
        """

        logger.info(f"Confirming appointment to reschedule: {appointment_id}")

        appointment = next(
            (a for a in self.retrieved_patient.appointments if a.id == appointment_id),
            None,
        )

        if not appointment:
            return f"Invalid appointment ID: {appointment_id}. Please try again."

        context.userdata.current_appointment = appointment

        appointment_motive = next(
            (m for m in self.clinic_config.motives if m.id == appointment.motive_id),
            None,
        )

        if not appointment_motive:
            return f"Invalid motive: {appointment.motive_name}. Please try again."

        logger.info(
            f"Found motive: {appointment_motive.name} ({appointment_motive.id})"
        )

        if (
            not context.userdata.motive
            or context.userdata.motive.id != appointment_motive.id
        ):
            context.userdata.motive = appointment_motive
            self.availabilities = []

        return "Now let's find availabilities for this motive"

    @function_tool()
    async def find_availabilities(
        self,
        context: RunContext_T,
        proposed_date: Annotated[
            Optional[str],
            Field(
                description="The date for which to retrieve availabilities, format is YYYY-MM-DD"
            ),
        ] = None,
    ) -> str:
        """
        Called to retrieve availabilities for the patient visit.
        """
        logger.info(f"Finding availabilities for {proposed_date}")
        if not context.userdata.motive:
            return "Appointment motive is not confirmed. Collect it before finding availabilities."

        parsed_date = (
            date.today()
            if not proposed_date
            else datetime.strptime(proposed_date, "%Y-%m-%d").date()
        )

        availabilities = await find_availabilities(
            context.userdata.clinic_config,
            context.userdata.motive,
            parsed_date,
        )

        self.availabilities += availabilities

        return f"""
            {f"Availabilities found for {proposed_date}" if proposed_date else "Closes availabilities"}:
            {"\n".join([f"  - {format_availability(a)}" for a in availabilities])}
        """

    @function_tool()
    async def confirm_appointment_datetime(
        self,
        context: RunContext_T,
        appointment_datetime: Annotated[
            str,
            Field(description="The appointment slot datetime, using ISO format"),
        ],
    ) -> str:
        """
        Called to check the slot the patient wants to book is available

        Args:
            appointment_datetime (datetime): The appointment slot datetime
        """
        logger.info(f"Checking availability on {appointment_datetime}")
        if not context.userdata.motive:
            return "Appointment motive is not confirmed. Collect it before finding availabilities."
        # Parse to naive datetime
        dt = datetime.fromisoformat(appointment_datetime)
        # Localize to Europe/Paris (UTC+2 in summer)
        dt = dt.replace(tzinfo=ZoneInfo("Europe/Paris"))
        # Format to ISO with milliseconds and offset
        formatted_appointment_datetime = dt.isoformat(timespec="milliseconds")
        found_availability = next(
            (
                a
                for a in self.availabilities
                if formatted_appointment_datetime == a.start_date
            ),
            None,
        )
        if not found_availability:
            return f"Invalid appointment datetime: {appointment_datetime}. Please try again."

        context.userdata.appointment_slot = found_availability
        return "Now let's confirm all the information before booking the appointment"

    @function_tool()
    async def confirm_reschedule(
        self,
        context: RunContext_T,
    ) -> str:
        """
        Called when the user confirms the appointment they want to reschedule and the new date and time.
        """
        if context.userdata.current_appointment is None:
            return "No appointment selected for rescheduling"

        logger.info(
            f"Confirming rescheduling of appointment {context.userdata.current_appointment.id} to {context.userdata.appointment_slot}"
        )
        await reschedule_appointment(context.userdata)
        context.userdata.has_completed_task = True
        await context.userdata.audio_player.play("./assets/ding.wav")
        return "Appointment rescheduled."
