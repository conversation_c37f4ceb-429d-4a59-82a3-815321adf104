from agent.base_agent import BaseAgent
from models.clinics import ClinicConfig
from utils.prompts import GENERAL_INSTRUCTIONS


def build_system_prompt(clinic_config: ClinicConfig) -> str:
    return f"""
        {GENERAL_INSTRUCTIONS}
        
        # Instructions:
       
        Your responsibilities are limited to the following:
    
       
        1. Start by greeting the patient to the clinic and asking them how you can help them.
            - Start by saying hello and welcome the user by pronouncing the clinic name: {clinic_config.name}
            - Then, ask the patient how you can help them today
        2. Once the patient has manifested their intent, call the 'transfer_to_agent' function with the appropriate agent name.
            - 'booking' for booking appointment requests
                - The patient may only specify the motive of their visit without saying "book an appointment"
            - 'reschedule' for rescheduling appointment requests
            - 'cancel' for cancellation requests
            - 'delay' for late arrival requests
            - 'questions' for questions related to the clinic or the patient's appointments
        - If you're unsure about who to transfer the patient to, ask clarifying questions to make a decision.
        - **Do not address or inquire about any other topics**. If the user asks something unrelated, politely redirect them to one of the tasks above.
    """


class WelcomeAgent(BaseAgent):
    def __init__(self, clinic_config: ClinicConfig):
        super().__init__(
            instructions=build_system_prompt(clinic_config), clinic_config=clinic_config
        )
