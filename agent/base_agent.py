import asyncio
from typing import Annotated, Any, AsyncIterable, Coroutine, Literal, Optional, Tu<PERSON>, Union

from livekit.agents import Agent, ModelSettings, function_tool
from livekit.agents.voice.transcription.filters import filter_markdown
from livekit.rtc import AudioFrame
from loguru import logger
from pydantic import Field

from models.clinics import ClinicConfig
from models.user_data import CallIntent, RunContext_T
from services.telephony import forward_call


class BaseAgent(Agent):
    def __init__(self, instructions: str, clinic_config: ClinicConfig, *args: Any, **kwargs: Any) -> None:
        self.clinic_config = clinic_config
        self.transfer_retries = 0
        super().__init__(
            instructions=instructions,
            *args,
            **kwargs,
        )

    async def on_enter(self) -> None:
        logger.info(f"Starting {self.__class__.__name__} workflow")
        await self.session.generate_reply(
            # Never allow interruptions if this is the first message
            # TODO: remove if we keep uninterruptions
            allow_interruptions=len(self.chat_ctx.items)
            > 1
        )

    def tts_node(
        self, text: AsyncIterable[str], model_settings: ModelSettings
    ) -> Union[AsyncIterable[AudioFrame], Coroutine[Any, Any, AsyncIterable[AudioFrame]], Coroutine[Any, Any, None]]:
        filtered_text = filter_markdown(text)
        return super().tts_node(filtered_text, model_settings)

    @function_tool()
    async def forward_call(
        self,
        context: RunContext_T,
        forward_number: Annotated[
            Optional[str], Field(description="The number to forward the call to")
        ],
    ) -> None:
        """
        Called whenever the patient wants to be transfered to a human agent
        """
        logger.info(f"Forwarding call to {forward_number}")
        number = forward_number or self.clinic_config.forward_number

        if not number:
            await self.session.generate_reply(
                instructions="Say that all our lines are busy and try again later. You cannot transfer the call."
            )
            return

        await self.session.generate_reply(
            instructions="""
                Explain that you are now going to transfer the patient to a human agent and that the call will be picked up shortly.
                Explain that all lines might be busy so they might not get answered immediately.
            """
        )

        await asyncio.sleep(3)

        room = context.userdata.ctx.room
        _, caller = next(iter(room.remote_participants.items()))

        await forward_call(
            caller_identity=caller.identity,
            room_name=room.name,
            forward_number=number,
        )

    # TODO: should it be a tool? Or a simple handler, call programmatically
    @function_tool()
    async def end_call(
        self,
        context: RunContext_T,
        should_leave_note: Annotated[
            bool, Field(description="Whether to leave a note to the reception")
        ] = False,
    ) -> Tuple[Agent, str]:
        """
        Called whenever the call should end.
        The user can never trigger this tool on their own.
        It must be called only when we reached a step where the call should end.
        """
        logger.info(f"Ending call: should_leave_note={should_leave_note}")

        questions_agent = await self.transfer_to_agent(context, "questions")
        # TODO: probably should let a few tools there
        # FIXME: an empty list make the agent crash
        await questions_agent.update_tools([self.end_call])
        return (
            questions_agent,
            """
                End the call on a polite, reassuring note. For instance, "*Merci de votre confiance, **à bientôt** et bonne journée !*". 
                You will now only be able to answer questions about the clinic and the medical practice. 
                If the patient wants to do another action like booking a new appointment or cancel an existing one, politely ask them to make a new call.
            """,
        )

    # TODO: probably just necessary to specify types of agents here and not in welcome agent
    @function_tool()
    async def transfer_to_agent(
        self,
        context: RunContext_T,
        agent_name: Literal[
            "booking", "reschedule", "cancel", "delay", "questions", "welcome"
        ],
    ) -> Agent:
        """Transfer the call to another agent.
        Never mention the existence of other agents to the patient. Simply say nothing and continue the conversation naturally.
        Do not call this tool if the patient wants something completely off of the scope of the clinic. Simply answer that you are not able to help them and propose to continue the conversation.

        Args:
            agent_name (enum): The name of the agent to transfer to.
            Possible values are:
            - "booking": for booking an appointment
            - "reschedule": for rescheduling an appointment
            - "cancel": for cancelling an appointment
            - "delay": for notifying the clinic that the patient will be late to their appointment
            - "questions": for answering questions about the clinic and the medical practice
            - "welcome": used to route the patient back to the main menu if they want to change their mind about their initial request
                - Examples:
                    - While in the booking flow, the patient asks a question about the clinic
                    - While in the questions flow, the patient wants to book an appointment or continue a booking flow
        """
        logger.info(f"Transferring to {agent_name}")
        next_agent = context.userdata.agents[agent_name]

        if agent_name not in ["questions", "welcome"]:
            context.userdata.intent = CallIntent(agent_name)

        await next_agent.update_chat_ctx(self.session.history)
        return next_agent
