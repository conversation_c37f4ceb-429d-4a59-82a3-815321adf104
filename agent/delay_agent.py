from datetime import date
from typing import Annotated, Optional

from livekit.agents import function_tool
from loguru import logger
from pydantic import Field

from agent.base_agent import BaseAgent
from models.clinics import ClinicConfig
from models.patients import DoctolibPatient, PatientIdentity
from models.user_data import RunContext_T
from services.appointments import notify_delay
from services.patients import RetrievePatientRequest, retrieve_existing_patient
from utils.prompts import GENERAL_INSTRUCTIONS, build_name_prompt


# TODO: not fan of the "no need to ask", we should definitely build something more robust
def build_system_prompt(retrived_patient: Optional[DoctolibPatient]) -> str:
    return f"""
        {GENERAL_INSTRUCTIONS}
        
        
        # Instructions:
       
        Your goal is to **guide the user through notifying the clinic that the patient will be late** from start to finish, following the steps below. 
        - Begin the flow immediately (do not introduce yourself with extra chit-chat) and lead the patient through each step, while adhering to the above guidelines. 
        - The delay notification is only successful when you have called the `confirm_delay` tool with all required details and the user has confirmed everything.
            - If the tool is not called, the delay notification is not considered successful, try to figure out what is blocking you and try to resolve it
            
        ### 1. Patient identity collection
        {build_name_prompt(retrived_patient)}
        **Important**: No need to ask if the patient already came to the clinic as we are in the delay notification flow.
        
        ### 2. Appointment selection
        Once the patient is identified, you should see the current pending appointments they have.
        - If the patient has only one appointment, ask them to confirm that it is the one they want to notify a delay for.
        - If the patient has no appointment, stop the flow and inform them that there is no appointment to notify a delay for.
        - If the patient has multiple appointments, ask them to confirm the one they want to notify a delay for.
        
        ### 3. Delay duration 
        Ask the patient how long they will be late for their appointment.
        
        ### 4. Delay notification confirmation
        Once the patient has selected the appointment and how long they will be late, confirm the delay with them.
            - If the patient confirms, call the `confirm_delay` tool with the appointment ID and the delay in minutes.
            - If the patient declines, ask them how they want to proceed.
        Once the notification is confirmed, inform the patient that the clinic has been notified and end the flow.
            - Call the `end_call` tool to end the conversation.
    """


class DelayAgent(BaseAgent):
    def __init__(
        self, clinic_config: ClinicConfig, retrieved_patient: Optional[DoctolibPatient]
    ):
        super().__init__(
            instructions=build_system_prompt(retrieved_patient),
            clinic_config=clinic_config,
        )

    @function_tool()
    async def confirm_identity(
        self,
        context: RunContext_T,
        first_name: Annotated[
            str,
            Field(
                description="The patient's first name, with correct spelling collected from the patient"
            ),
        ],
        last_name: Annotated[
            str,
            Field(
                description="The patient's last name, with correct spelling collected from the patient"
            ),
        ],
        birthdate: Annotated[
            date,
            Field(description="The patient's birthdate, collected from the patient"),
        ],
    ) -> str:
        """
        Called when the user provides their name.
        You will prompt the user with the full spelling of the name and confirm if it matches the patient's identity.
        If the user confirms, call the function again with `has_confirmed` set to `true`.

        Args:
            first_name (str): The patient's first name
            last_name (str): The patient's last name
            birthdate (str): The patient's birthdate in format YYYY-MM-DD
        """
        logger.info(
            f"Confirmed patient identity: {first_name} {last_name} ({birthdate})"
        )

        patient = await retrieve_existing_patient(
            context.userdata.config_id,
            params=RetrievePatientRequest(
                first_name=first_name, last_name=last_name, birthdate=birthdate
            ),
        )

        if not patient:
            return "I could not find your identity in our system. Please try again."

        self.retrieved_patient = patient
        context.userdata.patient = PatientIdentity(
            id=self.retrieved_patient.id,
            first_name=self.retrieved_patient.first_name,
            last_name=self.retrieved_patient.last_name,
            birthdate=self.retrieved_patient.birthdate,
        )

        if not len(patient.appointments):
            return "You have no appointments."

        return f"""
            You are identified as {first_name} {last_name}. 
            You have the following appointments:
            {"\n".join([f"  - {a.motive_name} on {a.start_date} (appointment_id: {a.id})" for a in patient.appointments])}
            Please confirm the appointment you want to notify a delay for.
        """

    @function_tool()
    async def confirm_delay(
        self,
        context: RunContext_T,
        appointment_id: Annotated[
            str,
            Field(description="The appointment ID to notify a delay for"),
        ],
        delay_minutes: Annotated[
            int,
            Field(description="The number of minutes the patient will be late"),
        ],
    ) -> str:
        """
        Called when the user confirms the appointment they want to notify a delay for and how long they will be late.

        Args:
            appointment_id (str): The appointment ID to
            delay_minutes (int): The number of minutes the patient will be late
        """
        logger.info(
            f"Confirming delay of for {delay_minutes} minutes for appointment {appointment_id}"
        )
        await notify_delay(self.clinic_config, appointment_id, delay_minutes)
        context.userdata.has_completed_task = True
        await context.userdata.audio_player.play("./assets/ding.wav")
        return "The clinic has been notified."
