from datetime import date, datetime
from typing import Annotated, List, Optional
from zoneinfo import ZoneInfo

import sentry_sdk
from livekit.agents.llm import function_tool
from loguru import logger
from pydantic import Field

from agent.base_agent import BaseAgent
from models.availabilities import Availability
from models.clinics import ClinicConfig
from models.patients import DoctolibPatient, PatientIdentity
from models.user_data import RunContext_T
from services.appointments import book_appointment
from services.availabilities import find_availabilities
from services.patients import RetrievePatientRequest, retrieve_existing_patient
from utils.dates import format_availability
from utils.prompts import GENERAL_INSTRUCTIONS, build_name_prompt

# Notes:
# - Was able to book appointment without birthdate
# - Still some problems with the name spelling
# - I saw the bot not answer after name recognition
# - <PERSON><PERSON> said "sorry we don't have a slot for dateX but I can propose dateX"....
# - <PERSON><PERSON> said 1st August 11am was not available
# - The bot does not always spell the name to double check
# - The bot does not always asks for a second appointment when imaging motives are mentioned
# - The bot is not super clear when answering questions


def build_global_prompt(
    clinic_config: ClinicConfig, retrived_patient: Optional[DoctolibPatient]
) -> str:
    motives = [m.name for m in clinic_config.motives]
    return f"""
        {GENERAL_INSTRUCTIONS}
      
         ---
    
        # Booking Flow 
        Your goal is to **guide the patient through booking a medical appointment** from start to finish, strictly following the steps below. Immediately start the flow (no introductions) and lead the patient efficiently and empathetically.
        
        The appointment booking succeeds only after you've called the `book_appointment` tool with all required details and received the patient's final confirmation.
        
        ## Step 1. Patient identity collection
        {build_name_prompt(retrived_patient)}
    
        ## Step 2. Motive Collection  
        Determine the **motive (reason)** for the appointment and confirm it with the patient.  
        - **Check for Existing Motive Info**: The patient may have already mentioned why they are calling. If you have a hint of the motive from earlier in the conversation, verify it. For example: *"Vous souhaitez prendre rendez-vous pour **une consultation de routine**, c’est bien cela ?"*. Only do this if you’re fairly sure; if not, just ask.  
        - **Ask for the Motive**: If the motive is unclear or not given yet, ask an open question: *"Quel est le **motif** de votre consultation ?"*. (Do **not** list all possible motives unsolicited, as that could overwhelm the user.)  
        - **Acceptable Motives**: You have a predefined list of possible motives (e.g., consultation types, follow-ups, imaging, etc.). Only those are valid. If the patient’s answer doesn’t match one of the known motives, do not invent a new one:
            {"\n".join([f"  - {m}" for m in motives])}
            - Instead, politely clarify or offer a close match. For instance, if they say something that isn’t on the list, respond with something like: *"Je ne suis pas sûr de comprendre. Est-ce pour **[closest motive]** ?"* or ask them to rephrase. If they ask "What can I book?", provide a **short** list of a few example motives relevant to what they hinted, up to 5 options at most.  
        - **Explicit Motive Confirmation**: Once you have identified a motive that matches one of the available options, **repeat it back to the patient for confirmation**. For example: *"Très bien, ce serait pour **un bilan sanguin**, c’est bien ça ?"*. Wait for the patient to confirm with a yes (or correct you if wrong). **Do not proceed to searching for appointments** until the patient clearly confirms the motive. This is crucial to ensure you’re booking the right type of appointment.  
        - After confirmation, call the `confirm_motive` tool with the chosen motive to log it, and acknowledge the confirmation (e.g., "*Parfait, nous allons planifier un rendez-vous pour **ce motif**.*").
        
        ## Step 3. Appointment Scheduling  
        Now, find an available date and time that suits the patient’s needs, and guide them to book it.  
        - **Retrieve Availabilities**: Let the patient know you will check the schedule (e.g., "*Un instant, je vérifie les disponibilités...*"). Use the `find_availabilities` tool to fetch the nearest available appointment slots for the confirmed motive and (implicitly) for this patient.  
        - **Present Options Clearly**: When the availability results return, present a summary of a few options in a **clear and concise** manner. Group similar times or days together so that the patient can easily grasp the choices. However, do not propose more than 2 groups:
            - For example, you might say: *"Les prochaines disponibilités sont **lundi et mercredi en fin d'après-midi (18h ou 19h)**, ou **mardi entre 15h00 et 16h30**."* This gives an intuitive overview rather than a long list.
            - If there are many options, prioritize a selection of the most reasonable ones (e.g., the earliest dates or times that were found, or those the patient hinted at preferring). You can always offer to look further if none of these suit the patient.
            - If there are no options, don't invent anything, apologize and ask the patient to call back later.
        - **Handle Specific Date/Time Requests**: If the patient asks for a **specific date or time** (e.g., "Do you have something on August 1st in the morning?" or "I’m only free after 6pm"), use the `find_availabilities` tool with the provided constraint (that date or time window) to check.
            - If there is availability matching the request, present those specific slots (e.g., "*Oui, le **1er août à 10h30** est disponible.*").
            - If there is no exact match (the requested day/time is fully booked or not possible), apologize and clearly state that *no* slots are available then. Then **proactively offer the closest alternatives**. For example: *"Désolé, il n’y a pas de disponibilité le **1er août à 11h**. Par contre, je peux vous proposer le **1er août à 14h00**, ou bien le **2 août** dans la matinée.*"*. This way, the patient isn’t left hanging with a denial – you immediately provide options near their preference.
        - **Iterate if Needed**: Give the patient a moment to consider the options. They might choose one, ask about a different day, or indicate none of the options work. Respond accordingly:
            - If they want a different time or day that wasn’t listed, happily oblige to search again with the new criteria (call `find_availabilities` again with the updated request).
            - If they seem unsure, you can gently ask which days or times generally work best and offer to find slots in that range.
        - **Confirm Selected Slot**: Once the patient selects a specific slot (date and time), repeat it back to them to make sure you have it right. For example: *"D’accord, nous partons sur le **mardi 5 septembre à 16h30**. Cela vous convient-il ?"*. If they confirm, proceed to lock in that slot by calling the `confirm_appointment_datetime` tool with that date and time. This reserves the time in the system (pending final booking).
        - **Imaging or Additional Appointments**: After scheduling the main appointment, check if the **motive involves an imaging exam or a follow-up** that needs separate scheduling:
            - *If yes (for example, the motive is a “MRI referral” or the doctor’s visit requires an X-ray beforehand)*, inform the patient and offer to help schedule that second appointment. E.g., "*Pour ce type de consultation, vous aurez également besoin d’un rendez-vous pour un examen d’imagerie. Voulez-vous que je vous aide à **prendre ce rendez-vous d’imagerie** maintenant ?*". If the patient says yes, treat it as a short new booking process: use `find_availabilities` for the imaging department and help pick a slot, then confirm it. (If the patient prefers to schedule it later or separately, just provide instructions or a number to call, and proceed to final confirmation.)
            - *If no additional appointments are needed*, or once all required appointments are chosen, move on to the final confirmation.

        ## Step 4. Final Confirmation and Booking
        Now finalize the appointment booking and make sure the patient has all the details:
        - **Review Details**: Clearly recap all the information that has been gathered and decided before booking. This includes: the patient’s name, birthdate, the motive, and the appointment date/time (plus any imaging appointment if one was scheduled). For example, in French: *"Très bien, je récapitule : **Alexandre Dubois, né le 15 mai 1975**, rendez-vous pour **une consultation de routine**, le **mardi 5 septembre à 16h30** (avec un examen d’imagerie associé le 1er septembre à 10h). Est-ce que tout est correct ?"*.
        - **Final Corrections**: Wait for the patient to confirm that this summary is all correct. If the patient notices any mistake or wants to change something (perhaps they misheard a date or need to adjust time), **address it immediately**:
            - If it’s a minor detail (e.g., spelling of name or a wrong birth year), fix that detail by revisiting the relevant step (without making them repeat everything).
            - If they want a different slot now, return to the scheduling step to find a better time.
            - Ensure that after any change, you reconfirm the detail with the patient.
        - **Book the Appointment**: Once the patient gives a final confirmation that all details are correct and they are ready to book, call the `book_appointment` tool with the confirmed information (patient identity, motive, date/time, etc.). This will finalize the appointment in the system.
        - **Confirmation to Patient**: After a successful booking, inform the patient in a friendly manner. For example: *"C’est bon, votre rendez-vous est **confirmé** ! Vous recevrez un SMS de confirmation d’ici quelques instants."*. Provide any other relevant information, such as the appointment reference number or instructions if applicable (e.g., "*N’oubliez pas d’apporter votre carte d’identité le jour du rendez-vous.*").
        - **Closing the Conversation**: Call the `end_call` tool to end the conversation.
    """


class BookingAgent(BaseAgent):
    def __init__(
        self, clinic_config: ClinicConfig, retrieved_patient: Optional[DoctolibPatient]
    ):
        self.retrieved_patient = retrieved_patient
        self.availabilities: List[Availability] = []

        system_prompt = build_global_prompt(clinic_config, retrieved_patient)
        super().__init__(instructions=system_prompt, clinic_config=clinic_config)

    @function_tool()
    async def confirm_identity(
        self,
        context: RunContext_T,
        first_name: Annotated[
            str,
            Field(
                description="The patient's first name, with correct spelling collected from the patient"
            ),
        ],
        last_name: Annotated[
            str,
            Field(
                description="The patient's last name, with correct spelling collected from the patient"
            ),
        ],
        birthdate: Annotated[
            date,
            Field(description="The patient's birthdate, collected from the patient"),
        ],
        is_new_patient: Annotated[
            bool,
            Field(
                description="Whether the patient is already known by the clinic or not"
            ),
        ],
    ) -> str:
        """
        Called when the user provides their name.
        You will prompt the user with the full spelling of the name and confirm if it matches the patient's identity.
        If the user confirms, call the function again with `has_confirmed` set to `true`.

        Args:
            first_name (str): The patient's first name
            last_name (str): The patient's last name
            birthdate (str): The patient's birthdate in format YYYY-MM-DD
            is_new_patient (bool): Whether the patient is already known by the clinic or not
        """
        logger.info(
            f"Confirmed patient identity: {first_name} {last_name} ({birthdate} | new_patient: {is_new_patient})"
        )

        context.userdata.patient = PatientIdentity(
            first_name=first_name,
            last_name=last_name,
            birthdate=birthdate,
        )

        if not is_new_patient:
            patient = await retrieve_existing_patient(
                context.userdata.config_id,
                params=RetrievePatientRequest(
                    first_name=first_name, last_name=last_name, birthdate=birthdate
                ),
            )
            self.retrieved_patient = patient
        else:
            self.retrieved_patient = None

        if self.retrieved_patient:
            if self.retrieved_patient.is_blocked:
                return """
                    Politely the patient you they are not allowed to book appointment at this clinic.
                    Tell them you will leave a note to the office.
                    Then, end the conversation with `end_call` and `should_leave_note` set to `True`.
                """

            context.userdata.patient = PatientIdentity(
                id=self.retrieved_patient.id,
                first_name=self.retrieved_patient.first_name,
                last_name=self.retrieved_patient.last_name,
                birthdate=self.retrieved_patient.birthdate,
            )
            return "We have found your identity in our system. Now let's confirm the visit motive."

        return "Now let's confirm the visit motive"

    @function_tool()
    async def confirm_motive(
        self,
        context: RunContext_T,
        motive: Annotated[
            str,
            Field(
                description="The patient's appointment motive collected from the list of available motives"
            ),
        ],
    ) -> str:
        """
        Called after you confirmed the patient's visit motive and they said yes.

        Args:
            motive (str): The patient's appointment motive collected from the list of available motives
        """

        logger.info(f"Confirming visit motive: {motive}")
        found_motive = next(
            (m for m in self.clinic_config.motives if m.name == motive), None
        )

        if not found_motive:
            return f"Invalid motive: {motive}. Please try again."
        logger.info(f"Found motive: {found_motive.name} ({found_motive.id})")

        if not context.userdata.motive or context.userdata.motive.id != found_motive.id:
            context.userdata.motive = found_motive
            self.availabilities = []

        return "Now let's find availabilities for this motive"

    @function_tool()
    async def find_availabilities(
        self,
        context: RunContext_T,
        proposed_date: Annotated[
            Optional[str],
            Field(
                description="The date for which to retrieve availabilities, format is YYYY-MM-DD"
            ),
        ] = None,
    ) -> str:
        """
        Called to retrieve availabilities for the patient visit.
        """
        logger.info(f"Finding availabilities for {proposed_date}")
        if not context.userdata.motive:
            return "Appointment motive is not confirmed. Collect it before finding availabilities."

        parsed_date = (
            date.today()
            if not proposed_date
            else datetime.strptime(proposed_date, "%Y-%m-%d").date()
        )

        availabilities = await find_availabilities(
            context.userdata.clinic_config,
            context.userdata.motive,
            parsed_date,
        )

        self.availabilities += availabilities

        return f"""
            {f"Availabilities found for {proposed_date}" if proposed_date else "Closes availabilities"}:
            {"\n".join([f"  - {format_availability(a)}" for a in availabilities])}
        """

    @function_tool()
    async def confirm_appointment_datetime(
        self,
        context: RunContext_T,
        appointment_datetime: Annotated[
            str,
            Field(description="The appointment slot datetime, using ISO format"),
        ],
    ) -> str:
        """
        Called to check the slot the patient wants to book is available

        Args:
            appointment_datetime (datetime): The appointment slot datetime
        """
        logger.info(f"Checking availability on {appointment_datetime}")
        if not context.userdata.motive:
            return "Appointment motive is not confirmed. Collect it before finding availabilities."
        # Parse to naive datetime
        dt = datetime.fromisoformat(appointment_datetime)
        # Localize to Europe/Paris (UTC+2 in summer)
        dt = dt.replace(tzinfo=ZoneInfo("Europe/Paris"))
        # Format to ISO with milliseconds and offset
        formatted_appointment_datetime = dt.isoformat(timespec="milliseconds")
        found_availability = next(
            (
                a
                for a in self.availabilities
                if formatted_appointment_datetime == a.start_date
            ),
            None,
        )
        if not found_availability:
            return f"Invalid appointment datetime: {appointment_datetime}. Please try again."

        context.userdata.appointment_slot = found_availability
        return "Now let's confirm all the information before booking the appointment"

    @function_tool()
    async def book_appointment(
        self,
        context: RunContext_T,
    ) -> str:
        """
        Called you have gathered all the necessary informations to actually book the appointment
        """

        logger.info(
            f"Booking appointment for {context.userdata.patient} for {context.userdata.motive} on {context.userdata.appointment_slot}"
        )

        if not context.userdata.patient:
            return "Patient identity is not confirmed. Collect it again before booking."
        if not context.userdata.motive:
            return (
                "Appointment motive is not confirmed. Collect it again before booking."
            )
        if not context.userdata.appointment_slot:
            return "Appointment datetime is not confirmed. Collect it again before booking."

        try:
            confirmed_booking = await book_appointment(context.userdata)
            context.userdata.booking = confirmed_booking
            context.userdata.has_completed_task = True
            await context.userdata.audio_player.play("./assets/ding.wav")
            return "The appointment has been successfully booked. You can ask the patient for other questions if necessary. Else, just thank them and end the conversation."
        except Exception as e:
            sentry_sdk.capture_exception(e)
            logger.error(f"Failed to book appointment: {e}")
            return "An error occurred while booking the appointment. Please try again."
