import asyncio

from livekit.agents import (AgentSession, AudioConfig, BackgroundAudioPlayer,
                            BuiltinAudioClip, ConversationItemAddedEvent,
                            MetricsCollectedEvent, UserStateChangedEvent,
                            metrics)
from livekit.agents.metrics import UsageCollector
from livekit.rtc import RemoteParticipant, Room
from loguru import logger

from services.calls import report_call
from utils.latency_logger import LatencyLogger


async def load_background_audio(room: Room, agent_session: AgentSession):
    background_audio = BackgroundAudioPlayer(
        # play office ambience sound looping in the background
        ambient_sound=AudioConfig(BuiltinAudioClip.OFFICE_AMBIENCE, volume=0.8),
        # play keyboard typing sound when the agent is thinking
        thinking_sound=[
            AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING, volume=0.8),
            AudioConfig(BuiltinAudioClip.KEYBOARD_TYPING2, volume=0.7),
        ],
    )
    await background_audio.start(room=room, agent_session=agent_session)
    return background_audio


# LiveKit event handlers
def handle_conversation_item_added(event: ConversationItemAddedEvent):
    logger.info(f"Message: [{event.item.role.capitalize()}] {event.item.content[0]}")


def handle_user_away(session: AgentSession):
    def on_user_away(event: UserStateChangedEvent):
        if event.new_state == "away":
            session.generate_reply(
                instructions="Politely ask the user 'Are you still there', then tell them you're listening and propose to continue the conversation"
            )
            session._update_user_state("listening")

    return on_user_away


def handle_session_end(session: AgentSession):
    def on_session_end(participant: RemoteParticipant):
        logger.info(f"Participant left: {participant.sid}")
        asyncio.create_task(report_call(session.userdata, session.history))

    return on_session_end


def handle_metrics_collected(
    usage_collector: UsageCollector, latency_logger: LatencyLogger
):
    def on_metrics_collected(ev: MetricsCollectedEvent):
        metrics.log_metrics(ev.metrics)
        latency_logger.aggregate_metrics(ev.metrics)
        usage_collector.collect(ev.metrics)

    return on_metrics_collected


def handle_log_usage(usage_collector: UsageCollector):
    async def on_log_usage():
        summary = usage_collector.get_summary()
        logger.info(f"Usage: {summary}")

    return on_log_usage
