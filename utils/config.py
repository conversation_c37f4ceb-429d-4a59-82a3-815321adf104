from livekit.agents import AutoSubscribe
from livekit.rtc import ParticipantKind
from twilio.rest.bulkexports.v1.export.job import JobContext

from models.calls import CallConfig

TEMP_CONFIG_MAP = {
    "+33974992469": "config7",
    "+33974992633": "config8",
    "+33974992630": "config9",
    "+33974992545": "config10",
    "+33974997841": "config11",
}


def get_dev_call_config(call_id: str) -> CallConfig:
    return CallConfig(
        call_id=call_id,
        is_dev=True,
        config_id="config7",
        caller_phone_number="+33664700736",
        # caller_phone_number="+33601548911",
        voice_speed=1.7,
    )


async def get_call_config(agent_name: str, ctx: JobContext) -> CallConfig:
    call_config = get_dev_call_config(f"dev_{ctx.job.id}")
    # Agent name is defined only in production
    if agent_name:
        await ctx.connect(auto_subscribe=AutoSubscribe.AUDIO_ONLY)
        participant = await ctx.wait_for_participant(
            kind=[ParticipantKind.PARTICIPANT_KIND_SIP]
        )
        attrs = participant.attributes

        clinic_phone_number = attrs.get("sip.trunkPhoneNumber")

        call_config = CallConfig(
            config_id=TEMP_CONFIG_MAP.get(clinic_phone_number),
            call_id=attrs.get("sip.twilio.callSid"),
            clinic_phone_number=clinic_phone_number,
            caller_phone_number=attrs.get("sip.phoneNumber"),
        )

    return call_config
