[project]
name = "livekit-poc"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"

dependencies = [
    "livekit-agents[deepgram,openai,silero,azure,turn-detector]>=1.2.1",
    "livekit-plugins-noise-cancellation>=0.2.5",
    "loguru>=0.7.3",
    "python-dotenv>=1.1.1",
    "sentry-sdk>=2.34.1",
    "supabase>=2.17.0",
    "torch>=2.7.1",
    "twilio>=9.7.0",
]

[dependency-groups]
dev = [
    "black>=25.1.0",
    "isort>=6.0.1",
    "mypy>=1.17.0",
    "pydantic>=2.11.7",
    "ruff>=0.12.4",
]
