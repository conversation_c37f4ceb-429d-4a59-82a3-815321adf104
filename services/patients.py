from datetime import date, datetime
from typing import Optional

from loguru import logger
from pydantic import BaseModel

from lib.doctolib_client import doctolib_client
from models.appointments import Appointment
from models.patients import DoctolibPatient


class RetrievePatientRequest(BaseModel):
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    birthdate: Optional[date] = None
    phone_number: Optional[str] = None


async def retrieve_existing_patient(config_id: str, params: RetrievePatientRequest):
    logger.info(f"Retrieving patient details from {params}")

    res = await doctolib_client.get(
        "users/search-patient",
        {
            "config": config_id,
            "first_name": params.first_name,
            "last_name": params.last_name,
            "birthdate": (
                params.birthdate.strftime("%d-%m-%Y") if params.birthdate else None
            ),
            "phone_number": params.phone_number,
        },
    )

    if res is None or len(res) == 0:
        logger.info(f"Did not find patient in Doctolib for {params}")
        return None

    raw_appointments = res[0].get("appointments", [])
    appointments = [
        Appointment(
            id=a["id"],
            start_date=datetime.fromisoformat(a["start_date"]),
            end_date=datetime.fromisoformat(a["end_date"]),
            motive_id=a["visit_motive_id"],
            motive_name=a["visit_motive_name"],
        )
        for a in raw_appointments
    ]

    doctolib_patient = DoctolibPatient(
        id=res[0]["id"],
        last_name=res[0]["last_name"],
        first_name=res[0]["first_name"],
        birthdate=res[0]["birthdate"],
        phone_number=res[0]["phone_number"],
        appointments=appointments,
        is_blocked=bool(res[0]["bounced_at"]),
    )
    logger.info(f"Found patient in Doctolib {doctolib_patient.id}")
    return doctolib_patient
