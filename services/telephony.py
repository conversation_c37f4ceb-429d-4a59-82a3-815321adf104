from livekit import api
from livekit.protocol.sip import TransferSIPParticipantRequest
from loguru import logger


# TODO: probably not the final signature
async def forward_call(
    caller_identity: str, room_name: str, forward_number: str
) -> None:
    async with api.LiveKitAPI() as livekit_api:
        transfer_to = f"tel:{forward_number}"

        # Create transfer request
        transfer_request = TransferSIPParticipantRequest(
            participant_identity=caller_identity,
            room_name=room_name,
            transfer_to=transfer_to,
            play_dialtone=False,
        )
        logger.debug(f"Transfer request: {transfer_request}")

        # Transfer caller
        await livekit_api.sip.transfer_sip_participant(transfer_request)
        logger.info(f"Successfully transferred participant {caller_identity}")
