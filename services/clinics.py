from typing import Optional

from loguru import logger

from lib.supabase_client import supabase_client
from models.clinics import ClinicConfig


async def get_clinc_config(
    config_id: Optional[str] = None,
    clinic_phone_number: Optional[str] = None,
) -> ClinicConfig:
    knowledge_base_query = supabase_client.table("knowledge-bases").select("*")

    if config_id:
        knowledge_base_query = knowledge_base_query.eq("config", config_id)
    elif clinic_phone_number:
        knowledge_base_query = knowledge_base_query.eq(
            "twilio_inbound_call_phone_number", clinic_phone_number
        )
    else:
        raise ValueError("Must provide either config_id or clinic_phone_number")

    knwoledge_base_response = knowledge_base_query.execute()
    knowledge_base = (
        knwoledge_base_response.data[0] if knwoledge_base_response.data else None
    )

    if not knowledge_base:
        # TEMP
        return await get_clinc_config("config7")
        # raise ValueError(
        #     f"No knowledge base found for the given config_id: {config_id} or clinic_phone_number: {clinic_phone_number}"
        # )

    clinic_config = ClinicConfig(
        config_id=knowledge_base["config"],
        name=knowledge_base["name"],
        address=knowledge_base["address"],
        openings=knowledge_base["openings_2"],
        additional_information=knowledge_base["additional_information"],
        motives=knowledge_base["inbound_config_file"]["visit-motives-categories"],
        agendas=knowledge_base["inbound_config_file"]["calendars"],
        forward_number=knowledge_base["forward_number"],
    )
    logger.info(f"Found clinic config: {clinic_config.name}")
    return clinic_config
