from datetime import datetime
from typing import List

from loguru import logger

from lib.doctolib_client import doctolib_client
from models.availabilities import Availability
from models.clinics import ClinicConfig, Motive

TEMP_CONFIG_AGENDAS_MAP = {
    "config7": [1242548, 1242550, 1318792, 1242576],  # Klarity Lille
    "config8": [1035200, 1035203, 1319116, 1035205],  # Klarity Lille
}


async def find_availabilities(
    clinic_config: ClinicConfig, motive: Motive, date: datetime.date
) -> List[Availability]:
    # agenda_ids = [agenda.id for agenda in clinic_config.agendas]
    agenda_ids = TEMP_CONFIG_AGENDAS_MAP.get(  # FIXME: custom for Klarity
        clinic_config.config_id, [agenda.id for agenda in clinic_config.agendas]
    )

    res = await doctolib_client.get(
        "appointments/availabilities",
        {
            "config": clinic_config.config_id,
            "day": str(date),
            "agenda_id": agenda_ids,
            "visit_motive_id": motive.id,
        },
    )

    if res is None or len(res) == 0:
        logger.warning(f"Did not find availabilities for motive {motive.id}")
        return []

    availabilities = [
        Availability(
            agenda_id=a["agenda_id"],
            start_date=a["start_date"],
            end_date=a["end_date"],
            medecin=a["medecin"],
        )
        for a in res
    ]

    logger.info(f"Found {len(availabilities)} availabilities for motive {motive.id}")
    return availabilities
