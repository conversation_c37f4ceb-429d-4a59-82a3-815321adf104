from livekit.agents import Job<PERSON>ontext

from agent.booking_agent import BookingAgent
from agent.cancel_agent import CancelAgent
from agent.delay_agent import DelayAgent
from agent.question_agent import QuestionsAgent
from agent.reschedule_agent import RescheduleAgent
from agent.welcome_agent import WelcomeAgent
from models.calls import CallConfig
from models.user_data import UserData
from services.clinics import get_clinc_config
from services.patients import RetrievePatientRequest, retrieve_existing_patient
from utils.livekit_config import setup_background_audio


async def prepare_user_data(call_config: CallConfig, job_ctx: JobContext) -> UserData:
    clinic_config = await get_clinc_config(
        call_config.config_id, call_config.clinic_phone_number
    )

    retrieved_patient = await retrieve_existing_patient(
        clinic_config.config_id,
        RetrievePatientRequest(phone_number=call_config.caller_phone_number),
    )

    user_data = UserData(
        config_id=clinic_config.config_id,
        call_config=call_config,
        clinic_config=clinic_config,
        retrieved_patient=retrieved_patient,
        agents={
            "welcome": WelcomeAgent(clinic_config=clinic_config),
            "booking": BookingAgent(
                clinic_config=clinic_config, retrieved_patient=retrieved_patient
            ),
            "reschedule": RescheduleAgent(
                clinic_config=clinic_config, retrieved_patient=retrieved_patient
            ),
            "cancel": CancelAgent(
                clinic_config=clinic_config, retrieved_patient=retrieved_patient
            ),
            "delay": DelayAgent(
                clinic_config=clinic_config, retrieved_patient=retrieved_patient
            ),
            "questions": QuestionsAgent(
                clinic_config=clinic_config, retrieved_patient=retrieved_patient
            ),
        },
        audio_player=setup_background_audio(),
        ctx=job_ctx,
    )

    return user_data
