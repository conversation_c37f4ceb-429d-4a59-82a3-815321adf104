from datetime import datetime

from livekit.agents import ChatContext, ChatMessage

from lib.n8n_client import n8n_client
from lib.twilio_client import fetch_twilio_recording_url
from models.user_data import CallIntent, UserData

INTENT_TO_TAG = {
    CallIntent.BOOKING: "Nouveau RDV",
    CallIntent.RESCHEDULE: "Modification RDV",
    CallIntent.CANCEL: "Annulation RDV",
    CallIntent.DELAY: "Retard",
    CallIntent.QUESTION: "Question/Tâche médicale",
    CallIntent.UNDEFINED: "Question/Tâche médicale",
}

# TODO: this is temporary to match Pipecat's enums
INTENT_TO_OLD_INTENTS = {
    CallIntent.BOOKING: "nouveau",
    CallIntent.RESCHEDULE: "modifier",
    CallIntent.CANCEL: "annuler",
    CallIntent.DELAY: "retard",
    CallIntent.QUESTION: "question",
    CallIntent.UNDEFINED: "start",
}


async def report_call(userdata: UserData, chat_ctx: ChatContext) -> None:
    if userdata.call_config.is_dev:
        return

    recording_url = await fetch_twilio_recording_url(userdata.call_config.call_id)
    conversation_transcript = "\n".join(
        [
            f"{item.role.capitalize()}: {item.content[0]}"
            for item in chat_ctx.items
            if isinstance(item, ChatMessage)
        ]
    )

    report_call_body = {
        "config": userdata.config_id,
        "call_duration": str(datetime.now() - userdata.call_config.start_time),
        "phone_caller": {
            "id": userdata.call_config.call_id,
            "phone_number": userdata.call_config.caller_phone_number,
            "recording_url": recording_url,
            "conversation_transcript": conversation_transcript,
            "intent": INTENT_TO_OLD_INTENTS.get(userdata.intent, "start"),
            "has_note": False,
            # "room_url": "",
            "has_forward_call": False,
            "successful_call": userdata.has_completed_task,
            "is_demo": False,
        },
        "call_type": INTENT_TO_TAG.get(userdata.intent, "None"),
    }

    if userdata.patient:
        report_call_body.update(
            {
                "patient_data": {
                    # "email": "",
                    "is_new_patient": not userdata.patient.id,
                    "first_name": userdata.patient.first_name,
                    "last_name": userdata.patient.last_name,
                    # "medecin_historique": "",
                    # "next_appointment": "",
                    # "bounced_at": "",
                    "phone_number": userdata.call_config.caller_phone_number,
                },
            }
        )
    elif userdata.retrieved_patient:
        report_call_body.update(
            {
                "patient_data": {
                    "is_new_patient": False,
                    "first_name": userdata.retrieved_patient.first_name,
                    "last_name": userdata.retrieved_patient.last_name,
                    "phone_number": userdata.call_config.caller_phone_number,
                },
            }
        )

    if userdata.booking:
        report_call_body.update(
            {
                "appointment_data": {
                    "id": userdata.booking.id,
                    "patient_id": userdata.booking.patient_id,
                    "is_booking_created": True,
                },
            }
        )

    res = await n8n_client.post(
        url="f71367a7-97aa-4dd9-a799-666f4c97cd93",
        json=report_call_body,
    )
