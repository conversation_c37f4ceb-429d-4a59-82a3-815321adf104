from typing import Optional


from lib.doctolib_client import doctolib_client
from lib.n8n_client import n8n_client
from models.clinics import ClinicConfig
from models.user_data import UserData, Booking


async def cancel_appointment(
    clinic_config: ClinicConfig,
    appointment_id: str,
    reason: Optional[str],
) -> None:
    await doctolib_client.put(
        "appointments/cancel",
        {
            "config": clinic_config.config_id,
            "id": appointment_id,
            "notes": f"[Vocca] {reason}",
        },
    )


async def reschedule_appointment(userdata: UserData) -> Booking:
    if userdata.current_appointment is None or userdata.appointment_slot is None:
        raise ValueError("Missing appointment data for rescheduling")

    await cancel_appointment(
        userdata.clinic_config,
        userdata.current_appointment.id,
        f"Rendez-vous déplacé au {userdata.appointment_slot.start_date}",
    )
    return await book_appointment(userdata)


async def notify_delay(
    clinic_config: ClinicConfig,
    appointment_id: str,
    delay_minutes: int,
) -> None:
    await doctolib_client.put(
        "appointments",
        {
            "config": clinic_config.config_id,
            "id": appointment_id,
            "notes": f"[Vocca] Le patient aura {delay_minutes} minutes de retard.",
        },
    )


async def book_appointment(userdata: UserData) -> Booking:
    if (
        userdata.appointment_slot is None
        or userdata.patient is None
        or userdata.motive is None
    ):
        raise ValueError("Missing required data for booking appointment")

    new_booking_info = {
        "callID": userdata.call_config.call_id,
        "config": userdata.config_id,
        "number": userdata.call_config.caller_phone_number,
        "agenda_id": userdata.appointment_slot.agenda_id,
        # "equipment_agenda_id": booking_info.equipment_agenda_id,
        "client": userdata.patient.full_name,
        "visit_motive_id": userdata.motive.id,
        "visit_motive_name": userdata.motive.name,
        # "type": booking_info.type,
        "start_date": userdata.appointment_slot.start_date,
        "end_date": userdata.appointment_slot.end_date,
        "patient_id": userdata.patient.id,
        # "medecin": booking_info.medecin,
        "first_name": userdata.patient.first_name,
        "last_name": userdata.patient.last_name,
        "birth_date": userdata.patient.birthdate.strftime("%Y-%m-%d"),
        "new_patient": not userdata.patient.id,
        # "notes": booking_info.notes,
        "company_data": userdata.clinic_config.model_dump(mode="json"),
        # "custom_fields_values": booking_info.custom_fields_values,
        # "steps": booking_info.steps,
        # "substitute": substitute,
    }

    res = await n8n_client.post(
        url="7627ef3c-9e1d-47d0-bbbf-a618a327cb20",
        json=new_booking_info,
    )

    if res is None or len(res) == 0:
        raise RuntimeError(
            f"Failed to create new booking for call {userdata.call_config.call_id}"
        )

    return Booking(
        id=res[0]["id"],
        patient_id=res[0]["patient"]["id"],
    )
