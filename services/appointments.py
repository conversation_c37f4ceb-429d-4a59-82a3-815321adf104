from typing import Optional

from lib.doctolib_client import doctolib_client
from lib.n8n_client import n8n_client
from models.clinics import ClinicConfig
from models.user_data import UserData


async def cancel_appointment(
    clinic_config: ClinicConfig,
    appointment_id: str,
    reason: Optional[str],
):
    res = await doctolib_client.put(
        "appointments/cancel",
        {
            "config": clinic_config.config_id,
            "id": appointment_id,
            "notes": f"[Vocca] {reason}",
        },
    )
    return res


async def reschedule_appointment(userdata: UserData):
    await cancel_appointment(
        userdata.clinic_config,
        userdata.current_appointment.id,
        f"Rendez-vous déplacé au {userdata.appointment_slot.start_date}",
    )
    res = await book_appointment(userdata)
    return res


async def notify_delay(
    clinic_config: ClinicConfig,
    appointment_id: str,
    delay_minutes: int,
):
    res = await doctolib_client.put(
        "appointments",
        {
            "config": clinic_config.config_id,
            "id": appointment_id,
            "notes": f"[Vocca] Le patient aura {delay_minutes} minutes de retard.",
        },
    )
    return res


async def book_appointment(userdata: UserData):
    new_meeting_info = {
        "callID": userdata.call_config.call_id,
        "config": userdata.config_id,
        "number": userdata.call_config.caller_phone_number,
        "agenda_id": userdata.appointment_slot.agenda_id,
        # "equipment_agenda_id": booking_info.equipment_agenda_id,
        "client": userdata.patient.full_name,
        "visit_motive_id": userdata.motive.id,
        "visit_motive_name": userdata.motive.name,
        # "type": booking_info.type,
        "start_date": userdata.appointment_slot.start_date,
        "end_date": userdata.appointment_slot.end_date,
        "patient_id": userdata.patient.id,
        # "medecin": booking_info.medecin,
        "first_name": userdata.patient.first_name,
        "last_name": userdata.patient.last_name,
        "birth_date": userdata.patient.birthdate.strftime("%Y-%m-%d"),
        "new_patient": not userdata.patient.id,
        # "notes": booking_info.notes,
        "company_data": userdata.clinic_config.model_dump(mode="json"),
        # "custom_fields_values": booking_info.custom_fields_values,
        # "steps": booking_info.steps,
        # "substitute": substitute,
    }

    res = await n8n_client.post(
        url="7627ef3c-9e1d-47d0-bbbf-a618a327cb20",
        json=new_meeting_info,
    )
    return res
