import asyncio
from abc import ABC
from typing import Any, Callable, Dict, Optional

import requests
import sentry_sdk


class HTTPClient(ABC):
    def __init__(self, base_url: str, headers: Dict[str, str]):
        self.base_url = base_url
        self.headers = headers

    async def _make_request(
        self, method: Callable[..., Any], url: str, json: Optional[Dict[str, Any]] = None
    ) -> Any:
        try:
            response = await asyncio.to_thread(
                method,
                url=f"{self.base_url}/{url}",
                headers=self.headers,
                json=json,
                params=None,
            )

            return response.json()
        except Exception as e:
            sentry_sdk.capture_exception(e)
            return None

    async def get(self, url: str, json: Optional[Dict[str, Any]] = None) -> Any:
        return await self._make_request(requests.get, url, json)

    async def post(self, url: str, json: Optional[Dict[str, Any]] = None) -> Any:
        return await self._make_request(requests.post, url, json)

    async def put(self, url: str, json: Optional[Dict[str, Any]] = None) -> Any:
        return await self._make_request(requests.put, url, json)

    async def delete(self, url: str, json: Optional[Dict[str, Any]] = None) -> Any:
        return await self._make_request(requests.delete, url, json)
