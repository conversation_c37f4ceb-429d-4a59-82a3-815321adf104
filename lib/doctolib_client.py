import os

from lib.http_client import HTTPClient

SECRET_DOCTOLIB_URL = os.getenv("SECRET_DOCTOLIB_URL")
SECRET_DOCTOLIB_TOKEN = os.getenv("SECRET_DOCTOLIB_TOKEN")


class DoctolibClient(HTTPClient):
    def __init__(self) -> None:
        if not SECRET_DOCTOLIB_URL or not SECRET_DOCTOLIB_TOKEN:
            raise ValueError("Missing Doctolib credentials")

        super().__init__(
            base_url=SECRET_DOCTOLIB_URL,
            headers={"Authorization": f"Bearer {SECRET_DOCTOLIB_TOKEN}"},
        )


doctolib_client = DoctolibClient()
