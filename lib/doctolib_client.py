import os

from lib.http_client import HTTPClient

SECRET_DOCTOLIB_URL = os.getenv("SECRET_DOCTOLIB_URL")
SECRET_DOCTOLIB_TOKEN = os.getenv("SECRET_DOCTOLIB_TOKEN")


class DoctolibClient(HTTPClient):
    def __init__(self):
        super().__init__(
            base_url=SECRET_DOCTOLIB_URL,
            headers={"Authorization": f"Bearer {SECRET_DOCTOLIB_TOKEN}"},
        )


doctolib_client = DoctolibClient()
