import os
from typing import Optional

from loguru import logger
from twilio.base.exceptions import TwilioRestException
from twilio.rest import Client

TWILIO_ACCOUNT_SID = os.getenv("TWILIO_ACCOUNT_SID")
TWILIO_AUTH_TOKEN = os.getenv("TWILIO_AUTH_TOKEN")
INTERNAL_SERVER_URL = "https://internal-server-8w13.onrender.com"

twilio_client = Client(TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN)


async def fetch_twilio_recording_url(call_id: str) -> Optional[str]:
    """Fetches the recording URL for a given call ID."""
    try:
        recordings = twilio_client.recordings.list(call_sid=call_id)
        if recordings:
            recording_uri = recordings[0].uri
            recording_id = recording_uri.split("/")[-1].replace(".json", "")
            recording_url = f"{INTERNAL_SERVER_URL}/api/v1/twilio/listen/{recording_id}"
            return recording_url
    except TwilioRestException as e:
        logger.error(f"Failed to fetch recording: {str(e)}")

    logger.warning("No recording found for the call.")
    return None
