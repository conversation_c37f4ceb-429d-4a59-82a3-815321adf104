[mypy]
strict = true
namespace_packages = True
explicit_package_bases = True

;No explicit Any
warn_return_any = True
disallow_any_generics = True
disallow_any_unimported = True
disallow_any_decorated = True
;TODO: used to allow untyped args/kwargs. Check if we can remove
disallow_incomplete_defs= False
;disallow_any_expr = True
;disallow_any_explicit = True


;Options enabled by "strict" rule
;https://chatgpt.com/share/6895b611-34e4-8006-b7cd-75f4ac4be33a
;disallow_untyped_defs = True
;disallow_incomplete_defs = True
;disallow_untyped_calls = True
;disallow_untyped_decorators = True
;disallow_any_generics = True
;disallow_subclassing_any = True
;no_implicit_optional = True
;warn_redundant_casts = True
;warn_unused_ignores = True
;warn_return_any = True
;warn_no_return = True
;warn_unreachable = True
;strict_equality = True
;check_untyped_defs = True
;no_implicit_reexport = True

;Check if those are interesting
;disallow_any_unimported = True
;ignore_missing_imports = True
;strict_optional = True
