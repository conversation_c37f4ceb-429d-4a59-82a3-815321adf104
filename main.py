import os
import sys

import sentry_sdk
from dotenv import load_dotenv
from livekit.agents import JobContext, JobProcess, WorkerOptions, cli, metrics
from livekit.agents.voice import AgentSession
from livekit.agents.voice.room_io import RoomInputOptions
from livekit.plugins import azure, noise_cancellation, openai, silero
from livekit.plugins.azure.tts import ProsodyConfig
from loguru import logger

from models.user_data import UserData
from services.user_data import prepare_user_data
from utils.config import get_call_config
from utils.latency_logger import LatencyLogger
from utils.livekit_config import (handle_conversation_item_added,
                                  handle_log_usage, handle_metrics_collected,
                                  handle_session_end, handle_user_away,
                                  load_background_audio)

load_dotenv()

SENTRY_DSN = os.getenv("SENTRY_DSN")
AGENT_NAME = os.getenv("AGENT_NAME", "")
IS_DEV = not AGENT_NAME

sentry_sdk.init(
    dsn=SENTRY_DSN,
    # Add data like request headers and IP for users,
    # see https://docs.sentry.io/platforms/python/data-management/data-collected/ for more info
    send_default_pii=True,
    # Set traces_sample_rate to 1.0 to capture 100%
    # of transactions for tracing.
    traces_sample_rate=1.0,
    environment="production" if not IS_DEV else "development",
)


def prewarm(proc: JobProcess) -> None:
    logger.info("Warming up")
    proc.userdata["vad"] = silero.VAD.load(
        min_speech_duration=0.1,
        min_silence_duration=0.8,
        prefix_padding_duration=0.5,
        max_buffered_speech=15,
        activation_threshold=0.5,
    )


async def entrypoint(ctx: JobContext) -> None:
    logger.info("Taking call")

    call_config = await get_call_config(AGENT_NAME, ctx)
    logger.remove()
    logger.add(
        sys.stdout,
        format=f"{{time:YYYY-MM-DDTHH:mm:ss.SSS}} - <level>{{level}}</level> - <yellow>{call_config.call_id}</yellow> | {{message}}",
        colorize=True,
    )
    logger.info(f"Call Config: {call_config}")

    userdata = await prepare_user_data(call_config, ctx)

    is_interruptible = call_config.config_id in ["config8", "config10"]

    session = AgentSession[UserData](
        userdata=userdata,
        stt=azure.STT(language="fr-FR", segmentation_silence_timeout_ms=300),
        llm=openai.LLM.with_azure(temperature=0),
        tts=azure.TTS(
            voice="fr-FR-DeniseNeural",
            prosody=ProsodyConfig(rate=call_config.voice_speed),
        ),
        vad=ctx.proc.userdata["vad"],
        turn_detection="vad",
        max_tool_steps=3,
        user_away_timeout=10,
        min_endpointing_delay=0.2,
        max_endpointing_delay=2,
        min_interruption_duration=0.7,
        min_interruption_words=1,
        preemptive_generation=True,
        allow_interruptions=is_interruptible,
        discard_audio_if_uninterruptible=False,
        # to use realtime model, replace the stt, llm, tts and vad with the following
        # llm=openai.realtime.RealtimeModel(voice="alloy"),
    )

    # Start session
    await session.start(
        agent=userdata.agents["welcome"],
        room=ctx.room,
        room_input_options=RoomInputOptions(
            video_enabled=False,
            noise_cancellation=noise_cancellation.BVCTelephony(),
        ),
    )

    # Background audio
    userdata.audio_player = await load_background_audio(ctx.room, session)

    # Events & metrics handlers
    latency_logger = LatencyLogger()
    usage_collector = metrics.UsageCollector()

    session.on("user_state_changed", handle_user_away(session))
    session.on("conversation_item_added", handle_conversation_item_added)
    session.on(
        "metrics_collected", handle_metrics_collected(usage_collector, latency_logger)
    )
    ctx.add_shutdown_callback(handle_log_usage(usage_collector))
    ctx.room.on("participant_disconnected", handle_session_end(session))


if __name__ == "__main__":
    logger.info(f"Starting agent {AGENT_NAME}")
    cli.run_app(
        WorkerOptions(
            prewarm_fnc=prewarm,
            entrypoint_fnc=entrypoint,
            agent_name=AGENT_NAME,
            drain_timeout=1800,
        )
    )
